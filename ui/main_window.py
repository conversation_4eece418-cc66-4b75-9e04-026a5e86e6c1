"""
主窗口界面
"""
from PySide6.QtWidgets import (QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                               QMenuBar, QStatusBar, QMessageBox, QApplication,
                               QHBoxLayout, QLabel, QPushButton, QFileDialog)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction, QIcon
from ui.account_manager import AccountManager
from ui.follow_manager import FollowManager
from ui.collect_manager import CollectManager
from ui.data_viewer import DataViewer
from core.account_service import AccountService
from core.official_service import OfficialService
from core.data_manager import data_manager
from config.settings import settings
from ui.message_box import CustomMessageBox
import os


class WindowSizeManager:
    """窗口大小管理器"""

    @staticmethod
    def get_optimal_window_size():
        """获取最佳窗口大小"""
        from PySide6.QtGui import QGuiApplication

        # 获取主屏幕
        screen = QGuiApplication.primaryScreen()
        if not screen:
            return 800, 600, 100, 100

        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 计算窗口大小（屏幕分辨率的1/4面积，即宽高各为1/2）
        window_width = screen_width // 2
        window_height = screen_height * 0.65

        # 设置最小和最大限制
        min_width = 800
        min_height = 600
        max_width = int(screen_width * 0.8)  # 最大不超过屏幕的80%
        max_height = int(screen_height * 0.8)

        # 应用限制
        window_width = max(min_width, min(window_width, max_width))
        window_height = max(min_height, min(window_height, max_height))

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        return window_width, window_height, x, y

    @staticmethod
    def get_screen_info():
        """获取屏幕信息"""
        from PySide6.QtGui import QGuiApplication

        screen = QGuiApplication.primaryScreen()
        if not screen:
            return {"width": 1920, "height": 1080, "dpi": 96}

        geometry = screen.geometry()
        dpi = screen.logicalDotsPerInch()

        return {
            "width": geometry.width(),
            "height": geometry.height(),
            "dpi": dpi
        }


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.account_service = AccountService()
        self.official_service = OfficialService()
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.update_status()

        # 定时更新状态栏
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次

        # 检查微信路径
        self.check_wechat_path()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("微信公众号自动化管理工具")

        # 检查是否有保存的窗口大小设置
        saved_width = settings.get('ui.window_width')
        saved_height = settings.get('ui.window_height')
        saved_x = settings.get('ui.window_x')
        saved_y = settings.get('ui.window_y')

        # 检查是否所有必要的设置都存在且有效
        has_valid_settings = (
                saved_width is not None and saved_width > 0 and
                saved_height is not None and saved_height > 0 and
                saved_x is not None and saved_y is not None
        )

        if has_valid_settings:
            # 使用保存的窗口大小和位置
            # 验证保存的大小是否合理
            saved_width, saved_height = self._validate_window_size_static(saved_width, saved_height)

            # 验证位置是否在屏幕范围内
            screen = WindowSizeManager.get_screen_info()
            saved_x = max(0, min(saved_x, screen['width'] - saved_width))
            saved_y = max(0, min(saved_y, screen['height'] - saved_height))

            self.setGeometry(saved_x, saved_y, saved_width, saved_height)
            print(f"使用保存的窗口设置: {saved_width}x{saved_height} at ({saved_x}, {saved_y})")
        else:
            # 使用窗口大小管理器获取最佳窗口大小
            window_width, window_height, x, y = WindowSizeManager.get_optimal_window_size()
            self.setGeometry(x, y, window_width, window_height)

            # 获取屏幕信息用于调试
            screen_info = WindowSizeManager.get_screen_info()
            print(f"屏幕分辨率: {screen_info['width']}x{screen_info['height']}")
            print(f"使用默认窗口大小: {window_width}x{window_height} (屏幕的1/4)")
            print(f"窗口位置: ({x}, {y})")

        # 设置最小窗口大小
        self.setMinimumSize(800, 600)

        # 设置扁平化样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
            QPushButton {
                font-size: 12px;
                font-weight: 500;
                min-height: 28px;
                padding: 6px 12px;
            }
            QMessageBox {
                font-size: 12px;
                min-width: 400px;
                min-height: 150px;
            }
            QMessageBox QPushButton {
                font-size: 12px;
                font-weight: 500;
                min-width: 80px;
                min-height: 32px;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                background-color: #2196F3;
                color: white;
            }
            QMessageBox QPushButton:hover {
                background-color: #1976D2;
            }
            QMessageBox QPushButton:pressed {
                background-color: #1565C0;
            }
            QMessageBox QLabel {
                font-size: 12px;
                padding: 10px;
                line-height: 1.4;
            }
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                background-color: #ffffff;
                margin-top: 0px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #666666;
                padding: 8px 16px;
                margin-right: 1px;
                border: none;
                font-weight: 400;
                font-size: 13px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
                font-weight: 500;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e0e0e0;
                color: #333333;
            }
            QMenuBar {
                background-color: #2196F3;
                color: white;
                padding: 2px;
                font-weight: 400;
                border: none;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 6px 10px;
                border: none;
            }
            QMenuBar::item:selected {
                background-color: #1976D2;
            }
            QMenu {
                background-color: white;
                border: 1px solid #e0e0e0;
                padding: 2px;
            }
            QMenu::item {
                padding: 6px 12px;
                border: none;
            }
            QMenu::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QStatusBar {
                background-color: #f5f5f5;
                color: #666666;
                border-top: 1px solid #e0e0e0;
                font-size: 11px;
                padding: 2px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        central_widget.setStyleSheet("background-color: #f8f9fa;")
        self.setCentralWidget(central_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 账号管理标签页
        self.account_manager = AccountManager()
        self.account_manager.accounts_changed.connect(self.on_accounts_changed)
        self.tab_widget.addTab(self.account_manager, "账号管理")

        # 关注管理标签页
        self.follow_manager = FollowManager()
        self.tab_widget.addTab(self.follow_manager, "批量关注")

        # 采集管理标签页
        self.collect_manager = CollectManager()
        self.tab_widget.addTab(self.collect_manager, "批量采集")

        # 数据查看器标签页
        self.data_viewer = DataViewer()
        self.tab_widget.addTab(self.data_viewer, "数据查看")

        # 设置布局
        layout = QVBoxLayout(central_widget)
        layout.addWidget(self.tab_widget)

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        # 导入账号
        import_action = QAction("导入账号", self)
        import_action.triggered.connect(self.import_accounts)
        file_menu.addAction(import_action)

        # 导出账号
        export_action = QAction("导出账号", self)
        export_action.triggered.connect(self.export_accounts)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 刷新数据
        refresh_action = QAction("刷新数据", self)
        refresh_action.triggered.connect(self.refresh_data)
        tools_menu.addAction(refresh_action)

        tools_menu.addSeparator()

        # 数据管理
        view_all_data_action = QAction("查看所有数据", self)
        view_all_data_action.triggered.connect(self.view_all_data)
        tools_menu.addAction(view_all_data_action)

        clear_data_action = QAction("清空数据文件", self)
        clear_data_action.triggered.connect(self.clear_data_files)
        tools_menu.addAction(clear_data_action)

        merge_data_action = QAction("合并账号数据", self)
        merge_data_action.triggered.connect(self.merge_account_data)
        tools_menu.addAction(merge_data_action)

        backup_data_action = QAction("备份数据文件", self)
        backup_data_action.triggered.connect(self.backup_data_files)
        tools_menu.addAction(backup_data_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()

        # 账号数量标签
        self.account_count_label = QLabel()
        self.status_bar.addWidget(self.account_count_label)

        # 公众号数量标签
        self.official_count_label = QLabel()
        self.status_bar.addWidget(self.official_count_label)

        # 分隔符
        self.status_bar.addPermanentWidget(QLabel(" | "))

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addPermanentWidget(self.status_label)

    def update_status(self):
        """更新状态栏"""
        try:
            account_count = self.account_service.get_account_count()
            official_count = self.official_service.get_official_count()

            self.account_count_label.setText(f"账号: {account_count}")
            self.official_count_label.setText(f"公众号: {official_count}")

        except Exception as e:
            self.status_label.setText(f"状态更新失败: {str(e)}")

    def on_accounts_changed(self):
        """账号列表变化时的处理"""
        self.update_status()
        # 刷新关注管理页面的账号列表
        self.follow_manager.load_accounts()

    def import_accounts(self):
        """导入账号"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入账号文件", "", "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    new_accounts = [line.strip() for line in f.readlines() if line.strip()]

                existing_accounts = self.account_service.load_accounts()
                added_count = 0

                for account in new_accounts:
                    if (self.account_service.validate_account(account) and
                            account not in existing_accounts):
                        if self.account_service.add_account(account):
                            added_count += 1

                self.account_manager.load_accounts()
                self.follow_manager.load_accounts()
                self.update_status()

                CustomMessageBox.information(self, "导入完成", f"成功导入 {added_count} 个账号")

            except Exception as e:
                CustomMessageBox.critical(self, "导入失败", f"导入文件失败: {str(e)}")

    def export_accounts(self):
        """导出账号"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出账号文件", "accounts.csv", "CSV文件 (*.csv);;文本文件 (*.txt)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                accounts = self.account_service.load_accounts()
                with open(file_path, 'w', encoding='utf-8') as f:
                    for account in accounts:
                        f.write(f"{account}\n")

                CustomMessageBox.information(self, "导出成功", f"账号已导出到: {file_path}")

            except Exception as e:
                CustomMessageBox.critical(self, "导出失败", f"导出文件失败: {str(e)}")

    def show_settings(self):
        """显示设置对话框"""
        from ui.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        if dialog.exec_() == dialog.accepted:
            # 设置已更新
            self.update_status()

    def _validate_window_size_static(self, width: int, height: int) -> tuple:
        """静态验证窗口大小（用于初始化时）"""
        from PySide6.QtGui import QGuiApplication

        # 获取屏幕大小
        screen = QGuiApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()
        else:
            screen_width = 1920
            screen_height = 1080

        # 设置最小和最大限制
        min_width = 800
        min_height = 600
        max_width = int(screen_width * 0.9)
        max_height = int(screen_height * 0.9)

        # 应用限制
        width = max(min_width, min(width, max_width))
        height = max(min_height, min(height, max_height))

        return width, height

    def refresh_data(self):
        """刷新数据"""
        try:
            self.account_manager.load_accounts()
            self.follow_manager.load_accounts()
            self.collect_manager.load_official_infos()
            self.data_viewer.load_data()
            self.update_status()

            CustomMessageBox.information(self, "刷新完成", "数据已刷新")

        except Exception as e:
            CustomMessageBox.critical(self, "刷新失败", f"刷新数据失败: {str(e)}")

    def view_all_data(self):
        """查看所有数据"""
        # 切换到数据查看器标签页
        self.tab_widget.setCurrentWidget(self.data_viewer)

        # 如果all.csv文件存在，加载它
        if os.path.exists("data/all.csv"):
            self.data_viewer.current_file = "data/all.csv"
            self.data_viewer.file_label.setText("当前文件: data/all.csv")
            self.data_viewer.load_data()
        else:
            CustomMessageBox.information(
                self, "提示",
                "all.csv文件不存在。请先进行采集操作。"
            )

    def clear_data_files(self):
        """清空数据文件"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有采集的数据文件吗？\n\n"
            "此操作将删除以下文件：\n"
            "• data/all.csv (所有采集数据)\n"
            "• data/ 目录下的所有公众号数据文件\n\n"
            "此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                deleted_files = []

                # 删除all.csv
                if os.path.exists("data/all.csv"):
                    os.remove("data/all.csv")
                    deleted_files.append("data/all.csv")

                # 删除data目录下的CSV文件
                data_dir = "data"
                if os.path.exists(data_dir):
                    for filename in os.listdir(data_dir):
                        if filename.endswith('.csv'):
                            file_path = os.path.join(data_dir, filename)
                            os.remove(file_path)
                            deleted_files.append(f"data/{filename}")

                # 刷新数据查看器
                self.data_viewer.load_data()

                if deleted_files:
                    QMessageBox.information(
                        self, "清空完成",
                        f"已删除 {len(deleted_files)} 个文件:\n" + "\n".join(deleted_files)
                    )
                else:
                    QMessageBox.information(self, "清空完成", "没有找到需要删除的数据文件")

            except Exception as e:
                QMessageBox.critical(self, "清空失败", f"清空数据文件时出错:\n{str(e)}")

    def merge_account_data(self):
        """合并账号数据到all.csv"""
        reply = QMessageBox.question(
            self, "确认合并",
            "确定要将data目录下的所有账号数据合并到data/all.csv吗？\n\n"
            "此操作将：\n"
            "• 读取data目录下的所有CSV文件\n"
            "• 将数据合并到data/all.csv文件中\n"
            "• 自动去除重复的链接\n\n"
            "建议在操作前先备份数据文件。",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 执行合并
                result = data_manager.merge_account_data_to_all()

                # 刷新数据查看器
                self.data_viewer.refresh_file_list()
                self.data_viewer.load_data()

                QMessageBox.information(
                    self, "合并完成",
                    f"数据合并完成！\n\n"
                    f"新增记录数: {result['total_added']}\n"
                    f"去重记录数: {result['duplicates_removed']}"
                )

            except Exception as e:
                QMessageBox.critical(self, "合并失败", f"合并数据时出错:\n{str(e)}")

    def backup_data_files(self):
        """备份数据文件"""
        try:
            backup_dir = data_manager.backup_data_files()

            reply = QMessageBox.question(
                self, "备份完成",
                f"数据文件已备份到:\n{backup_dir}\n\n"
                "是否打开备份目录？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                import subprocess
                subprocess.Popen(f'explorer "{os.path.abspath(backup_dir)}"')

        except Exception as e:
            QMessageBox.critical(self, "备份失败", f"备份数据文件时出错:\n{str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            "微信公众号自动化管理工具\n\n"
            "版本: 1.0.0\n"
            "基于 PySide6 开发\n\n"
            "功能:\n"
            "• 公众号账号管理\n"
            "• 批量关注公众号\n"
            "• 批量采集公众号内容\n"
            "• 任务进度监控\n"
            "• 操作日志记录"
        )

    def closeEvent(self, event):
        """关闭事件"""
        # 只有在用户手动调整窗口大小时才保存，避免覆盖设置中的配置
        # 检查当前窗口大小是否与设置中的不同
        # current_width = self.width()
        # current_height = self.height()
        current_x = self.x()
        current_y = self.y()

        # settings_width = settings.get('ui.window_width')
        # settings_height = settings.get('ui.window_height')

        # # 如果窗口大小与设置不同，说明用户手动调整了，需要保存
        # if (settings_width != current_width or settings_height != current_height):
        #     settings.set('ui.window_width', current_width)
        #     settings.set('ui.window_height', current_height)
        #     settings.set('ui.window_x', current_x)
        #     settings.set('ui.window_y', current_y)
        #     print(f"保存用户调整的窗口大小: {current_width}x{current_height}")
        # else:
        #     # 只保存位置，不覆盖大小设置
        settings.set('ui.window_x', current_x)
        settings.set('ui.window_y', current_y)

        # 检查是否有任务正在运行
        if (self.follow_manager.task_manager.is_task_running() or
                self.collect_manager.task_manager.is_task_running()):

            reply = QMessageBox.question(
                self, "确认退出",
                "有任务正在运行，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

            # 取消正在运行的任务
            self.follow_manager.task_manager.cancel_current_task()
            self.collect_manager.task_manager.cancel_current_task()

        event.accept()

    def check_wechat_path(self):
        """检查微信路径设置"""
        wechat_path = settings.get_wechat_path()

        if not wechat_path:
            # 尝试自动检测
            if settings.auto_detect_and_save_wechat_path():
                wechat_path = settings.get('wechat.wechat_path')
                QMessageBox.information(
                    self, "微信路径检测",
                    f"已自动检测到微信路径:\n{wechat_path}\n\n"
                    "您可以在 工具 -> 设置 中修改此路径。"
                )
            else:
                # 提示用户手动设置
                reply = QMessageBox.question(
                    self, "微信路径未设置",
                    "未检测到微信安装路径，这可能影响关注和采集功能的正常使用。\n\n"
                    "是否现在设置微信路径？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.show_settings()
        else:
            # 验证路径是否有效
            if not os.path.exists(wechat_path):
                reply = QMessageBox.warning(
                    self, "微信路径无效",
                    f"配置的微信路径不存在:\n{wechat_path}\n\n"
                    "是否重新检测或设置微信路径？",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    self.show_settings()
