"""
账号管理界面
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QListWidget, QListWidgetItem, QLineEdit, QLabel,
                               QInputDialog, QFileDialog, QTextEdit, QGroupBox)
from PySide6.QtCore import Qt, Signal
from core.account_service import AccountService
from ui.message_box import CustomMessageBox


class AccountManager(QWidget):
    """账号管理界面"""

    accounts_changed = Signal()  # 账号列表变化信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.account_service = AccountService()
        self.setup_ui()
        self.load_accounts()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 设置扁平化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: 500;
                border: 1px solid #e0e0e0;
                margin-top: 8px;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #333333;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 12px;
                font-weight: 400;
                min-width: 60px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QLineEdit {
                padding: 8px 10px;
                border: 1px solid #e0e0e0;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QListWidget {
                border: 1px solid #e0e0e0;
                background-color: white;
                alternate-background-color: #fafafa;
                font-size: 12px;
                padding: 3px;
                outline: none;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #f0f0f0;
                margin: 1px;
                border-radius: 4px;
                color: #333333;
                background-color: transparent;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
                border: 1px solid #e0e0e0;
                color: #333333;
            }
            QListWidget::item:selected {
                background-color: #2196F3;
                color: white;
                border: 1px solid #1976D2;
                font-weight: 500;
            }
            QListWidget::item:selected:hover {
                background-color: #1976D2;
                color: white;
                border: 1px solid #1565C0;
            }
            QListWidget::item:focus {
                outline: none;
                border: 2px solid #2196F3;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
        """)

        # 顶部工具栏 - 将所有组件放在同一行
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(8)

        # 添加新账号组件（紧凑版）
        add_group = QGroupBox()
        add_group.setMaximumHeight(70)
        add_group_layout = QHBoxLayout(add_group)
        add_group_layout.setContentsMargins(8, 5, 8, 5)
        add_group_layout.setSpacing(6)

        self.account_input = QLineEdit()
        self.account_input.setPlaceholderText("输入公众号账号")
        self.account_input.returnPressed.connect(self.add_account)
        self.account_input.setStyleSheet("""
            QLineEdit {
                min-width: 170px;
                font-size: 12px;
                padding: 6px 10px;
                min-height: 20px;
            }
            QLineEdit::placeholder {
                color: #999999;
                font-style: normal;
            }
        """)
        add_group_layout.addWidget(self.account_input)

        self.add_btn = QPushButton("添加")
        self.add_btn.clicked.connect(self.add_account)
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 12px;
                min-width: 30px;
                min-height: 18px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        add_group_layout.addWidget(self.add_btn)

        toolbar_layout.addWidget(add_group)

        # 操作工具组件（紧凑版）
        tools_group = QGroupBox("操作工具")
        tools_group.setMaximumHeight(70)
        tools_layout = QHBoxLayout(tools_group)
        tools_layout.setContentsMargins(8, 5, 8, 5)
        tools_layout.setSpacing(4)

        # 优化按钮样式 - 更清晰的字体
        compact_button_style = """
            QPushButton {
                border: none;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 11px;
                min-width: 30px;
                min-height: 18px;
                border-radius: 4px;
            }
        """

        self.edit_btn = QPushButton("编辑")
        self.edit_btn.clicked.connect(self.edit_account)
        self.edit_btn.setStyleSheet(compact_button_style + """
            QPushButton {
                background-color: #FF9800;
                color: white;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        tools_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_account)
        self.delete_btn.setStyleSheet(compact_button_style + """
            QPushButton {
                background-color: #F44336;
                color: white;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """)
        tools_layout.addWidget(self.delete_btn)

        self.import_btn = QPushButton("导入")
        self.import_btn.clicked.connect(self.import_accounts)
        self.import_btn.setStyleSheet(compact_button_style + """
            QPushButton {
                background-color: #00BCD4;
                color: white;
            }
            QPushButton:hover {
                background-color: #00ACC1;
            }
        """)
        tools_layout.addWidget(self.import_btn)

        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self.export_accounts)
        self.export_btn.setStyleSheet(compact_button_style + """
            QPushButton {
                background-color: #9C27B0;
                color: white;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        tools_layout.addWidget(self.export_btn)

        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.clear_accounts)
        self.clear_btn.setStyleSheet(compact_button_style + """
            QPushButton {
                background-color: #757575;
                color: white;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        tools_layout.addWidget(self.clear_btn)

        toolbar_layout.addWidget(tools_group)

        # 统计信息组件（紧凑版）
        stats_group = QGroupBox("公众号数量统计")
        stats_group.setMaximumHeight(60)
        stats_layout = QHBoxLayout(stats_group)
        stats_layout.setContentsMargins(8, 5, 8, 5)

        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                font-weight: 500;
                color: #2196F3;
                padding: 6px 10px;
                background-color: #e3f2fd;
                border: 1px solid #2196F3;
                min-width: 80px;
            }
        """)
        stats_layout.addWidget(self.stats_label)

        toolbar_layout.addWidget(stats_group)
        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # 账号列表（调整大小）
        list_group = QGroupBox("账号列表")
        list_layout = QVBoxLayout(list_group)
        list_layout.setContentsMargins(8, 8, 8, 8)

        self.account_list = QListWidget()
        self.account_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.account_list.customContextMenuRequested.connect(self.show_context_menu)
        self.account_list.setStyleSheet("""
            QListWidget {
                min-height: 400px;
                font-size: 12px;
                border: 1px solid #e0e0e0;
                background-color: white;
                outline: none;
            }
            QListWidget::item {
                padding: 8px 12px;
                margin: 1px;
                border-radius: 4px;
                color: #333333;
                background-color: transparent;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
                border: 1px solid #e0e0e0;
                color: #333333;
            }
            QListWidget::item:selected {
                background-color: #2196F3;
                color: white;
                border: 1px solid #1976D2;
                font-weight: 500;
            }
            QListWidget::item:selected:hover {
                background-color: #1976D2;
                color: white;
                border: 1px solid #1565C0;
            }
            QListWidget::item:focus {
                outline: none;
                border: 2px solid #2196F3;
            }
        """)
        list_layout.addWidget(self.account_list)
        layout.addWidget(list_group)

    def load_accounts(self):
        """加载账号列表"""
        self.account_list.clear()
        accounts = self.account_service.load_accounts()

        for i, account in enumerate(accounts):
            # 创建列表项
            item = QListWidgetItem()

            # 设置显示文本，添加序号
            display_text = f"{i + 1:3d}. {account}"
            item.setText(display_text)

            # 存储原始账号数据
            item.setData(Qt.UserRole, account)

            # 设置工具提示
            item.setToolTip(f"账号ID: {account}\n点击选择，双击编辑")

            # 添加到列表
            self.account_list.addItem(item)

        self.update_stats()

    def update_stats(self):
        """更新统计信息"""
        count = self.account_list.count()
        self.stats_label.setText(f"总计: {count} 个账号")

    def add_account(self):
        """添加账号"""
        account = self.account_input.text().strip()
        if not account:
            CustomMessageBox.warning(self, "警告", "请输入账号ID")
            return

        if not self.account_service.validate_account(account):
            CustomMessageBox.warning(self, "警告", "账号格式不正确")
            return

        if self.account_service.add_account(account):
            self.account_input.clear()
            self.load_accounts()
            self.accounts_changed.emit()
            CustomMessageBox.information(self, "成功", f"账号 {account} 添加成功")
        else:
            CustomMessageBox.warning(self, "警告", "账号已存在")

    def edit_account(self):
        """编辑账号"""
        current_item = self.account_list.currentItem()
        if not current_item:
            CustomMessageBox.warning(self, "警告", "请选择要编辑的账号")
            return

        # 获取原始账号数据
        old_account = current_item.data(Qt.UserRole)
        new_account, ok = QInputDialog.getText(
            self, "编辑账号", "请输入新的账号ID:", text=old_account
        )

        if ok and new_account.strip():
            if not self.account_service.validate_account(new_account):
                CustomMessageBox.warning(self, "警告", "账号格式不正确")
                return

            if self.account_service.update_account(old_account, new_account.strip()):
                self.load_accounts()
                self.accounts_changed.emit()
                CustomMessageBox.information(self, "成功", "账号更新成功")
            else:
                CustomMessageBox.warning(self, "警告", "更新失败")

    def delete_account(self):
        """删除账号"""
        current_item = self.account_list.currentItem()
        if not current_item:
            CustomMessageBox.warning(self, "警告", "请选择要删除的账号")
            return

        # 获取原始账号数据
        account = current_item.data(Qt.UserRole)
        reply = CustomMessageBox.question(
            self, "确认删除", f"确定要删除账号 {account} 吗？"
        )

        if reply == CustomMessageBox.Yes:
            if self.account_service.remove_account(account):
                self.load_accounts()
                self.accounts_changed.emit()
                CustomMessageBox.information(self, "成功", "账号删除成功")

    def import_accounts(self):
        """导入账号"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入账号文件", "", "CSV文件 (*.csv);;文本文件 (*.txt);;所有文件 (*)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    new_accounts = [line.strip() for line in f.readlines() if line.strip()]

                existing_accounts = self.account_service.load_accounts()
                added_count = 0

                for account in new_accounts:
                    if self.account_service.validate_account(account) and account not in existing_accounts:
                        if self.account_service.add_account(account):
                            added_count += 1

                self.load_accounts()
                self.accounts_changed.emit()
                CustomMessageBox.information(self, "导入完成", f"成功导入 {added_count} 个账号")

            except Exception as e:
                CustomMessageBox.critical(self, "导入失败", f"导入文件失败: {str(e)}")

    def export_accounts(self):
        """导出账号"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出账号文件", "accounts.csv", "CSV文件 (*.csv);;文本文件 (*.txt)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                accounts = self.account_service.load_accounts()
                with open(file_path, 'w', encoding='utf-8') as f:
                    for account in accounts:
                        f.write(f"{account}\n")

                CustomMessageBox.information(self, "导出成功", f"账号已导出到: {file_path}")

            except Exception as e:
                CustomMessageBox.critical(self, "导出失败", f"导出文件失败: {str(e)}")

    def clear_accounts(self):
        """清空账号"""
        reply = CustomMessageBox.question(
            self, "确认清空", "确定要清空所有账号吗？此操作不可恢复！"
        )

        if reply == CustomMessageBox.Yes:
            self.account_service.save_accounts([])
            self.load_accounts()
            self.accounts_changed.emit()
            CustomMessageBox.information(self, "成功", "账号列表已清空")

    def show_context_menu(self, position):
        """显示右键菜单"""
        from PySide6.QtWidgets import QMenu

        menu = QMenu(self)

        edit_action = menu.addAction("编辑")
        edit_action.triggered.connect(self.edit_account)

        delete_action = menu.addAction("删除")
        delete_action.triggered.connect(self.delete_account)

        menu.exec_(self.account_list.mapToGlobal(position))

    def get_selected_accounts(self):
        """获取选中的账号列表"""
        selected_items = self.account_list.selectedItems()
        return [item.data(Qt.UserRole) for item in selected_items if item.data(Qt.UserRole)]
