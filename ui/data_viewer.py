"""
数据查看器界面
"""
import csv
import os
import webbrowser
from typing import List, Dict, Any

from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QAction
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTableWidget, QTableWidgetItem, QLabel,
                               QLineEdit, QComboBox, QGroupBox,
                               QHeaderView, QAbstractItemView, QMenu, QApplication,
                               QSplitter, QFileDialog, QMessageBox)

from core.data_manager import data_manager
from ui.components.data_stats import DataStatsWidget
from ui.message_box import CustomMessageBox


class DataLoadThread(QThread):
    """数据加载线程"""

    data_loaded = Signal(list, list)  # 数据, 表头
    error_occurred = Signal(str)  # 错误信息

    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path

    def run(self):
        """加载CSV数据"""
        try:
            # 使用数据管理器读取数据
            headers, data = data_manager.read_csv_data(self.file_path)
            self.data_loaded.emit(data, headers)

        except Exception as e:
            self.error_occurred.emit(str(e))


class DataViewer(QWidget):
    """数据查看器界面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_file = "all.csv"
        self.original_data = []
        self.filtered_data = []
        self.headers = []
        self.load_thread = None
        self.available_files = []
        self.setup_ui()
        self.refresh_file_list()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 15, 20, 15)

        # 设置扁平化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: 500;
                border: 1px solid #e0e0e0;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #333333;
                font-size: 13px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                min-width: 60px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
            QLineEdit, QComboBox {
                padding: 6px 8px;
                border: 1px solid #e0e0e0;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #2196F3;
                outline: none;
            }
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
                border: 1px solid #e0e0e0;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                border-right: 1px solid #e0e0e0;
                font-weight: 500;
                color: #333333;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
        """)

        # 创建主分割器（左右布局）
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                width: 2px;
            }
        """)

        # 左侧：统计信息区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 10, 0)
        left_layout.setSpacing(10)

        # 统计信息组件
        self.stats_widget = DataStatsWidget()
        self.stats_widget.setMaximumWidth(250)
        left_layout.addWidget(self.stats_widget)
        left_layout.addStretch()

        main_splitter.addWidget(left_widget)

        # 右侧：数据表格区域
        right_widget = QWidget()
        table_layout = QVBoxLayout(right_widget)
        table_layout.setContentsMargins(10, 0, 0, 0)
        table_layout.setSpacing(12)

        # 优化的操作工具栏
        toolbar_group = QGroupBox("操作工具")
        toolbar_group.setMaximumHeight(120)
        toolbar_layout = QVBoxLayout(toolbar_group)
        toolbar_layout.setContentsMargins(12, 8, 12, 8)
        toolbar_layout.setSpacing(8)

        # 第一行：文件选择和主要操作
        top_row = QHBoxLayout()
        top_row.setSpacing(12)

        # 文件选择区域
        file_container = QWidget()
        file_layout = QHBoxLayout(file_container)
        file_layout.setContentsMargins(0, 0, 0, 0)
        file_layout.setSpacing(8)

        file_label = QLabel("数据文件:")
        file_label.setStyleSheet("""
            font-weight: 500;
            color: #333333;
            font-size: 12px;
            min-width: 60px;
        """)
        file_layout.addWidget(file_label)

        self.file_selector = QComboBox()
        self.file_selector.currentTextChanged.connect(self.on_file_changed)
        self.file_selector.setMinimumWidth(180)
        self.file_selector.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 6px 10px;
                min-height: 24px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox:hover {
                border-color: #2196F3;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        file_layout.addWidget(self.file_selector)

        top_row.addWidget(file_container)

        # 操作按钮区域
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                border: none;
                padding: 6px 12px;
                font-weight: 500;
                font-size: 12px;
                min-width: 70px;
                min-height: 28px;
                border-radius: 4px;
            }
        """

        self.refresh_files_btn = QPushButton("刷新文件")
        self.refresh_files_btn.clicked.connect(self.refresh_file_list)
        self.refresh_files_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.refresh_files_btn)

        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #00BCD4;
                color: white;
            }
            QPushButton:hover {
                background-color: #00ACC1;
            }
        """)
        button_layout.addWidget(self.export_btn)

        top_row.addWidget(button_container)

        top_row.addStretch()
        toolbar_layout.addLayout(top_row)

        # 第二行：搜索功能
        search_row = QHBoxLayout()
        search_row.setSpacing(12)

        # 搜索区域
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(8)

        search_label = QLabel("数据搜索:")
        search_label.setStyleSheet("""
            font-weight: 500;
            color: #333333;
            font-size: 12px;
            min-width: 60px;
        """)
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索...")
        self.search_input.textChanged.connect(self.filter_data)
        self.search_input.setMinimumWidth(180)
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 10px;
                min-height: 24px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QLineEdit::placeholder {
                color: #999999;
                font-style: normal;
            }
        """)
        search_layout.addWidget(self.search_input)

        search_row.addWidget(search_container)

        search_row.addStretch()
        toolbar_layout.addLayout(search_row)

        table_layout.addWidget(toolbar_group)

        # 数据表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        # 设置表格样式
        # self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.table.horizontalHeader().setVisible(False)
        self.table.verticalHeader().setVisible(False)
        self.table.setStyleSheet("""
            QTableWidget {
                min-height: 500px;
            }
        """)

        table_layout.addWidget(self.table)

        # 状态信息
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("font-size: 10px; color: #666666; padding: 2px;")
        table_layout.addWidget(self.status_label)

        main_splitter.addWidget(right_widget)

        # 设置分割器比例 (左:右 = 1:3) - 统计信息:数据表格
        main_splitter.setSizes([200, 600])

        layout.addWidget(main_splitter)

    def load_data(self):
        """加载数据"""
        if not os.path.exists(self.current_file):
            self.status_label.setText(f"文件不存在: {self.current_file}")
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return

        self.status_label.setText("正在加载数据...")

        # 使用线程加载数据
        if self.load_thread and self.load_thread.isRunning():
            self.load_thread.quit()
            self.load_thread.wait()

        self.load_thread = DataLoadThread(self.current_file)
        self.load_thread.data_loaded.connect(self.on_data_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()

    def on_data_loaded(self, data: List[List[str]], headers: List[str]):
        """数据加载完成"""
        self.original_data = data
        self.headers = headers
        self.filtered_data = data.copy()

        self.update_table()
        self.status_label.setText(f"已加载 {len(data)} 条记录")

        # 更新统计信息
        self.stats_widget.update_stats()

    def on_load_error(self, error_msg: str):
        """数据加载错误"""
        self.status_label.setText(f"加载失败: {error_msg}")
        QMessageBox.critical(self, "加载失败", f"加载数据时出错:\n{error_msg}")

    def update_table(self):
        """更新表格显示"""
        if not self.headers:
            return

        # 设置表格大小
        self.table.setRowCount(len(self.filtered_data))
        self.table.setColumnCount(len(self.headers))
        self.table.setHorizontalHeaderLabels(self.headers)

        # 填充数据
        for row_idx, row_data in enumerate(self.filtered_data):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为只读
                self.table.setItem(row_idx, col_idx, item)

        # 调整列宽
        self.table.resizeColumnsToContents()

        # 限制最大列宽
        for i in range(self.table.columnCount()):
            if self.table.columnWidth(i) > 300:
                self.table.setColumnWidth(i, 500)

    def filter_data(self):
        """筛选数据"""
        search_text = self.search_input.text().lower()
        if not search_text:
            self.filtered_data = self.original_data.copy()
        else:
            self.filtered_data = []
            for row in self.original_data:
                if any(search_text in str(cell).lower() for cell in row):
                    self.filtered_data.append(row)

        self.update_table()
        self.status_label.setText(f"显示 {len(self.filtered_data)} / {len(self.original_data)} 条记录")

    def export_data(self):
        """导出数据"""
        if not self.filtered_data:
            CustomMessageBox.warning(self, "警告", "没有数据可导出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", f"exported_data.csv", "CSV文件 (*.csv)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(self.headers)  # 写入表头
                    writer.writerows(self.filtered_data)  # 写入数据

                CustomMessageBox.information(self, "导出成功", f"数据已导出到: {file_path}")

            except Exception as e:
                CustomMessageBox.critical(self, "导出失败", f"导出数据时出错: {str(e)}")

    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # 复制单元格
        copy_cell_action = QAction("复制单元格", self)
        copy_cell_action.triggered.connect(self.copy_cell)
        menu.addAction(copy_cell_action)

        # 复制行
        copy_row_action = QAction("复制整行", self)
        copy_row_action.triggered.connect(self.copy_row)
        menu.addAction(copy_row_action)

        # 如果是链接列，添加打开链接和下载文章选项
        current_item = self.table.itemAt(position)
        if current_item and self.is_url(current_item.text()):
            menu.addSeparator()
            open_url_action = QAction("打开链接", self)
            open_url_action.triggered.connect(lambda: self.open_url(current_item.text()))
            menu.addAction(open_url_action)

            # 添加复制链接选项
            copy_action = QAction("复制链接", self)
            copy_action.triggered.connect(lambda: self.copy_link(current_item.text()))
            menu.addAction(copy_action)

        menu.exec_(self.table.mapToGlobal(position))

    def copy_cell(self):
        """复制单元格内容"""
        current_item = self.table.currentItem()
        if current_item:
            clipboard = QApplication.clipboard()
            clipboard.setText(current_item.text())

    def copy_row(self):
        """复制整行内容"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            row_data = []
            for col in range(self.table.columnCount()):
                item = self.table.item(current_row, col)
                row_data.append(item.text() if item else "")

            clipboard = QApplication.clipboard()
            clipboard.setText("\t".join(row_data))

    def is_url(self, text: str) -> bool:
        """判断文本是否为URL"""
        return text.startswith(('http://', 'https://'))

    def open_url(self, url: str):
        """打开URL"""
        try:
            webbrowser.open(url)
        except Exception as e:
            CustomMessageBox.warning(self, "打开失败", f"无法打开链接: {str(e)}")

    def copy_link(self, url):
        """复制链接"""
        try:
            # 设置UI日志回调
            try:
                from core.official_down import set_ui_log_callback
                # 创建一个简单的日志函数，因为数据查看器没有日志组件
                def simple_log(message, level):
                    print(f"[{level}] {message}")

                set_ui_log_callback(simple_log)
            except ImportError:
                pass

            # 获取文章标题（从当前行的标题列获取）
            current_row = self.table.currentRow()
            title = "未知标题"

            if current_row >= 0:
                # 查找标题列
                for col in range(self.table.columnCount()):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item:
                        header_text = header_item.text().lower()
                        if 'title' in header_text or '标题' in header_text:
                            title_item = self.table.item(current_row, col)
                            if title_item:
                                title = title_item.text()
                            break

            # 复制URL到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(url)

            # 只复制URL到剪贴板
            CustomMessageBox.information(
                self, "URL已复制",
                f"URL已复制到剪贴板: {title}"
            )

        except Exception as e:
            CustomMessageBox.critical(self, "操作失败", f"复制链接时出错: {str(e)}")

    def refresh_file_list(self):
        """刷新文件列表"""
        # 使用数据管理器获取文件列表
        self.available_files = data_manager.get_all_data_files()

        # 更新下拉框
        self.file_selector.clear()
        for file_path, display_name in self.available_files:
            self.file_selector.addItem(display_name, file_path)

        # 设置当前选中的文件
        current_index = -1
        for i, (file_path, _) in enumerate(self.available_files):
            if file_path == self.current_file:
                current_index = i
                break

        if current_index >= 0:
            self.file_selector.setCurrentIndex(current_index)
        elif self.available_files:
            self.current_file = self.available_files[0][0]
            self.file_selector.setCurrentIndex(0)

    def on_file_changed(self):
        """文件选择改变"""
        current_data = self.file_selector.currentData()
        if current_data and current_data != self.current_file:
            self.current_file = current_data
            self.load_data()

    def get_file_info(self) -> Dict[str, Any]:
        """获取当前文件信息"""
        info = {
            "file_path": self.current_file,
            "file_name": os.path.basename(self.current_file),
            "is_total_file": self.current_file == "all.csv",
            "is_account_file": self.current_file.startswith("data/"),
            "record_count": len(self.original_data),
            "filtered_count": len(self.filtered_data),
            "columns": len(self.headers),
            "headers": self.headers.copy()
        }

        if info["is_account_file"]:
            # 从文件路径提取账号ID
            filename = os.path.basename(self.current_file)
            info["account_id"] = filename[:-4] if filename.endswith('.csv') else filename

        return info

    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        return {
            "total_records": len(self.original_data),
            "filtered_records": len(self.filtered_data),
            "columns": len(self.headers),
            "file_path": self.current_file,
            "headers": self.headers.copy()
        }
