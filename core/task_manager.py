"""
任务管理器 - 子进程模式
"""
import json
import logging
import os
import sys
from typing import List, Optional

from PySide6.QtCore import Signal, QObject, QProcess

from config.settings import settings
from core.official_service import OfficialInfo


class TaskSignals(QObject):
    """任务信号类"""
    progress = Signal(int, str)  # 进度, 消息
    finished = Signal(bool, str)  # 是否成功, 消息
    log = Signal(str)  # 日志消息


class FollowTask:
    """关注任务 - 子进程模式"""

    def __init__(self, accounts: List[str]):
        self.accounts = accounts
        self.signals = TaskSignals()
        self.is_cancelled = False
        self.process = QProcess()
        self.process.readyReadStandardOutput.connect(self.read_output)
        self.process.readyReadStandardError.connect(self.read_error)
        self.process.finished.connect(self.process_finished)
        self.current_index = 0
        self.total_accounts = len(accounts)

        # # 添加超时检测
        # from PySide6.QtCore import QTimer
        # self.timeout_timer = QTimer()
        # self.timeout_timer.timeout.connect(self.check_process_timeout)
        # self.timeout_timer.setSingleShot(True)

    def start(self):
        """开始关注任务"""
        try:
            self.signals.log.emit(f"开始关注任务，共 {len(self.accounts)} 个账号")

            # 检查微信路径
            wechat_path = settings.get_wechat_path()
            if not wechat_path:
                self.signals.log.emit("未配置微信路径，请在设置中配置")
                self.signals.finished.emit(False, "未配置微信路径")
                return

            # 创建临时配置文件
            config_data = {
                'accounts': self.accounts,
                'wechat_path': wechat_path,
                'task_type': 'follow'
            }

            config_file = os.path.join(os.getcwd(), 'temp_follow_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            # 检测是否在打包环境中 (兼容PyInstaller和Nuitka)
            is_packaged = (getattr(sys, 'frozen', False) or
                           hasattr(sys, '_MEIPASS') or
                           '__compiled__' in globals())

            if is_packaged:
                # 打包环境：使用主程序作为子进程，传递特殊参数
                main_exe = sys.executable
                args = ['--subprocess-worker', config_file]

                print(f"[DEBUG] 打包环境，启动子进程: {main_exe} {' '.join(args)}")

                # 设置工作目录为exe所在目录
                exe_dir = os.path.dirname(main_exe)
                self.process.setWorkingDirectory(exe_dir)
                print(f"[DEBUG] 设置工作目录: {exe_dir}")

                self.process.start(main_exe, args)
            else:
                # 开发环境：使用Python解释器执行脚本
                python_exe = sys.executable
                current_dir = os.path.dirname(os.path.abspath(__file__))
                script_path = os.path.join(current_dir, 'subprocess_worker.py')

                print(f"[DEBUG] 开发环境，启动子进程: {python_exe} {script_path} {config_file}")

                # 设置工作目录为项目根目录
                project_root = os.path.dirname(current_dir)
                self.process.setWorkingDirectory(project_root)
                print(f"[DEBUG] 设置工作目录: {project_root}")

                self.process.start(python_exe, [script_path, config_file])

            if not self.process.waitForStarted(5000):
                error_msg = f"子进程启动失败: {self.process.errorString()}"
                self.signals.log.emit(error_msg)
                self.signals.finished.emit(False, error_msg)
                return

            self.signals.log.emit("关注任务子进程已启动")
            print(f"[DEBUG] 关注任务子进程PID: {self.process.processId()}")

            # # 启动超时检测 (10分钟超时)
            # self.timeout_timer.start(600000)
            # print("[DEBUG] 已启动关注任务超时检测 (10分钟)")

        except Exception as e:
            self.signals.log.emit(f"关注任务启动异常: {str(e)}")
            self.signals.finished.emit(False, f"关注任务启动失败: {str(e)}")

    def read_output(self):
        """读取子进程标准输出"""
        try:
            data = self.process.readAllStandardOutput().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    try:
                        # 尝试解析JSON格式的消息
                        msg = json.loads(line.strip())
                        if msg.get('type') == 'log':
                            self.signals.log.emit(msg.get('message', ''))
                        elif msg.get('type') == 'progress':
                            progress = msg.get('progress', 0)
                            message = msg.get('message', '')
                            self.signals.progress.emit(progress, message)
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，直接作为日志输出
                        self.signals.log.emit(line.strip())
        except Exception as e:
            self.signals.log.emit(f"读取子进程输出时出错: {str(e)}")

    def read_error(self):
        """读取子进程错误输出"""
        try:
            data = self.process.readAllStandardError().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    self.signals.log.emit(f"[ERROR] {line.strip()}")
        except Exception as e:
            self.signals.log.emit(f"读取子进程错误输出时出错: {str(e)}")

    def process_finished(self, exit_code, exit_status):
        """子进程完成"""
        try:
            # 停止超时检测
            if hasattr(self, 'timeout_timer'):
                self.timeout_timer.stop()
                print("[DEBUG] 已停止关注任务超时检测")

            print(f"[DEBUG] 关注任务子进程结束，退出代码: {exit_code}, 状态: {exit_status}")
            self.signals.log.emit(f"子进程结束，退出代码: {exit_code}")

            # 清理临时配置文件
            config_file = os.path.join(os.getcwd(), 'temp_follow_config.json')
            if os.path.exists(config_file):
                os.remove(config_file)
                print(f"[DEBUG] 已清理配置文件: {config_file}")

            # 注意：不在这里清理关注结果文件，让UI层读取后再清理
            # 这样可以确保关注结果能被正确传递到UI层

            if exit_code == 0:
                self.signals.progress.emit(100, "关注任务完成")
                self.signals.finished.emit(True, f"关注任务完成，共处理 {len(self.accounts)} 个账号")
                print(f"[DEBUG] 关注任务成功完成")
            else:
                pass
                # error_msg = f"关注任务失败，退出代码: {exit_code}"
                # self.signals.finished.emit(False, error_msg)
                # print(f"[DEBUG] 关注任务失败: {error_msg}")

        except Exception as e:
            error_msg = f"处理子进程完成时出错: {str(e)}"
            self.signals.log.emit(error_msg)
            self.signals.finished.emit(False, error_msg)
            print(f"[DEBUG] 处理子进程完成异常: {error_msg}")

        finally:
            # 确保任务状态被清理
            print("[DEBUG] FollowTask process_finished 完成，准备清理任务状态")

    def cancel(self):
        """取消任务"""
        print("[DEBUG] 收到关注任务取消请求")
        self.is_cancelled = True

        # 终止子进程
        if self.process.state() == QProcess.Running:
            self.process.kill()
            if not self.process.waitForFinished(3000):
                self.process.terminate()
            print("[DEBUG] 已终止关注任务子进程")

        # 清理临时配置文件
        try:
            config_file = os.path.join(os.getcwd(), 'temp_follow_config.json')
            if os.path.exists(config_file):
                os.remove(config_file)
        except Exception as e:
            print(f"[DEBUG] 清理配置文件时出错: {e}")

        # 立即发送取消信号
        self.signals.finished.emit(False, "任务已取消")
        print("[DEBUG] 已发送关注任务取消信号")

    def check_process_timeout(self):
        """检查子进程超时"""
        if self.process.state() == QProcess.Running:
            print("[DEBUG] 关注任务子进程超时，强制终止")
            self.signals.log.emit("关注任务超时，正在强制终止...")

            # 强制终止子进程
            self.process.kill()
            if not self.process.waitForFinished(3000):
                self.process.terminate()

            self.signals.finished.emit(False, "关注任务超时")


class CollectTask:
    """采集任务 - 子进程模式"""

    def __init__(self, official_infos: List[OfficialInfo], limit_count: int = None, stop_exist_count: int = None, download_config: dict = None):
        super().__init__()
        self.official_infos = official_infos
        self.limit_count = limit_count or settings.get('task.limit_count', 200)
        self.stop_exist_count = stop_exist_count or settings.get('task.stop_exist_count', 30)
        self.signals = TaskSignals()
        self.is_cancelled = False
        self.process = QProcess()
        self.process.readyReadStandardOutput.connect(self.read_output)
        self.process.readyReadStandardError.connect(self.read_error)
        self.process.finished.connect(self.process_finished)
        self.current_index = 0
        self.total_officials = len(official_infos)
        self.download_config = download_config or {'enable': False, 'format': 'HTML', 'delay': 2}

        # 添加超时检测
        # from PySide6.QtCore import QTimer
        # self.timeout_timer = QTimer()
        # self.timeout_timer.timeout.connect(self.check_process_timeout)
        # self.timeout_timer.setSingleShot(True)

    def start(self):
        """开始采集任务"""
        try:
            self.signals.log.emit(f"开始采集任务，共 {len(self.official_infos)} 个公众号")
            self.signals.log.emit(f"采集参数: limit_count={self.limit_count}, stop_exist_count={self.stop_exist_count}")

            # 检查微信路径
            wechat_path = settings.get_wechat_path()
            if not wechat_path:
                self.signals.log.emit("未配置微信路径，请在设置中配置")
                self.signals.finished.emit(False, "未配置微信路径")
                return
            # 创建临时配置文件
            config_data = {
                'official_infos': [info.to_dict() for info in self.official_infos],
                'limit_count': self.limit_count,
                'stop_exist_count': self.stop_exist_count,
                'wechat_path': wechat_path,
                'task_type': 'collect',
                'download_config': self.download_config
            }

            config_file = os.path.join(os.getcwd(), 'temp_collect_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            # 检测是否在打包环境中 (兼容PyInstaller和Nuitka)
            is_packaged = (getattr(sys, 'frozen', False) or
                           hasattr(sys, '_MEIPASS') or
                           '__compiled__' in globals())

            if is_packaged:
                # 打包环境：使用主程序作为子进程，传递特殊参数
                main_exe = sys.executable
                args = ['--subprocess-worker', config_file]

                print(f"[DEBUG] 打包环境，启动子进程: {main_exe} {' '.join(args)}")

                # 设置工作目录为exe所在目录
                exe_dir = os.path.dirname(main_exe)
                self.process.setWorkingDirectory(exe_dir)
                print(f"[DEBUG] 设置工作目录: {exe_dir}")

                self.process.start(main_exe, args)
            else:
                # 开发环境：使用Python解释器执行脚本
                python_exe = sys.executable
                current_dir = os.path.dirname(os.path.abspath(__file__))
                script_path = os.path.join(current_dir, 'subprocess_worker.py')

                print(f"[DEBUG] 开发环境，启动子进程: {python_exe} {script_path} {config_file}")

                # 设置工作目录为项目根目录
                project_root = os.path.dirname(current_dir)
                self.process.setWorkingDirectory(project_root)
                print(f"[DEBUG] 设置工作目录: {project_root}")

                self.process.start(python_exe, [script_path, config_file])

            if not self.process.waitForStarted(5000):
                error_msg = f"子进程启动失败: {self.process.errorString()}"
                self.signals.log.emit(error_msg)
                self.signals.finished.emit(False, error_msg)
                return

            self.signals.log.emit("采集任务子进程已启动")
            print(f"[DEBUG] 采集任务子进程PID: {self.process.processId()}")

            # # 启动超时检测 (10分钟超时)
            # self.timeout_timer.start(600000)
            # print("[DEBUG] 已启动采集任务超时检测 (10分钟)")
        except Exception as e:
            logging.exception(e)
            self.signals.log.emit(f"采集任务启动异常: {str(e)}")
            self.signals.finished.emit(False, f"采集任务启动失败: {str(e)}")

    def read_output(self):
        """读取子进程标准输出"""
        try:
            data = self.process.readAllStandardOutput().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    try:
                        # 尝试解析JSON格式的消息
                        msg = json.loads(line.strip())
                        if msg.get('type') == 'log':
                            self.signals.log.emit(msg.get('message', ''))
                        elif msg.get('type') == 'progress':
                            progress = msg.get('progress', 0)
                            message = msg.get('message', '')
                            self.signals.progress.emit(progress, message)
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，直接作为日志输出
                        self.signals.log.emit(line.strip())
        except Exception as e:
            self.signals.log.emit(f"读取子进程输出时出错: {str(e)}")

    def read_error(self):
        """读取子进程错误输出"""
        try:
            data = self.process.readAllStandardError().data().decode('utf-8', errors='ignore')
            for line in data.strip().split('\n'):
                if line.strip():
                    self.signals.log.emit(f"[ERROR] {line.strip()}")
        except Exception as e:
            self.signals.log.emit(f"读取子进程错误输出时出错: {str(e)}")

    def process_finished(self, exit_code, exit_status):
        """子进程完成"""
        try:
            # 停止超时检测
            if hasattr(self, 'timeout_timer'):
                self.timeout_timer.stop()
                print("[DEBUG] 已停止采集任务超时检测")

            print(f"[DEBUG] 采集任务子进程结束，退出代码: {exit_code}, 状态: {exit_status}")
            self.signals.log.emit(f"子进程结束，退出代码: {exit_code}")

            # 清理临时配置文件
            config_file = os.path.join(os.getcwd(), 'temp_collect_config.json')
            if os.path.exists(config_file):
                os.remove(config_file)

            if exit_code == 0:
                self.signals.progress.emit(100, "采集任务完成")
                self.signals.finished.emit(True, f"采集任务完成，共处理 {len(self.official_infos)} 个公众号")
            else:
                pass
                # self.signals.finished.emit(False, f"采集任务失败，退出代码: {exit_code}")
        except Exception as e:
            self.signals.log.emit(f"处理子进程完成时出错: {str(e)}")
            self.signals.finished.emit(False, f"处理子进程完成时出错: {str(e)}")

    def cancel(self):
        """取消任务"""
        print("[DEBUG] 收到采集任务取消请求")
        self.is_cancelled = True

        # 终止子进程
        if self.process.state() == QProcess.Running:
            self.process.kill()
            if not self.process.waitForFinished(3000):
                self.process.terminate()
            print("[DEBUG] 已终止采集任务子进程")

        # 清理临时配置文件
        try:
            config_file = os.path.join(os.getcwd(), 'temp_collect_config.json')
            if os.path.exists(config_file):
                os.remove(config_file)
        except Exception as e:
            print(f"[DEBUG] 清理配置文件时出错: {e}")

        # 立即发送取消信号
        self.signals.finished.emit(False, "任务已取消")
        print("[DEBUG] 已发送采集任务取消信号")

    def check_process_timeout(self):
        """检查子进程超时"""
        if self.process.state() == QProcess.Running:
            print("[DEBUG] 关注任务子进程超时，强制终止")
            self.signals.log.emit("关注任务超时，正在强制终止...")

            # 强制终止子进程
            self.process.kill()
            if not self.process.waitForFinished(3000):
                self.process.terminate()

            self.signals.finished.emit(False, "关注任务超时")


class TaskManager:
    """任务管理器"""

    def __init__(self):
        self.current_task: Optional[QObject] = None

    def start_follow_task(self, accounts: List[str]) -> FollowTask:
        """启动关注任务"""
        if self.current_task:
            raise RuntimeError("已有任务正在运行")

        self.current_task = FollowTask(accounts)

        # 连接任务完成信号，自动清理任务状态
        self.current_task.signals.finished.connect(self._on_task_finished)

        self.current_task.start()
        return self.current_task

    def start_collect_task(self, official_infos: List[OfficialInfo],
                           limit_count: int = None, stop_exist_count: int = None, download_config: dict = None) -> CollectTask:
        """启动采集任务"""
        if self.current_task:
            raise RuntimeError("已有任务正在运行")

        self.current_task = CollectTask(official_infos, limit_count, stop_exist_count, download_config)

        # 连接任务完成信号，自动清理任务状态
        self.current_task.signals.finished.connect(self._on_task_finished)

        self.current_task.start()
        return self.current_task

    def cancel_current_task(self):
        """取消当前任务"""
        if self.current_task:
            print("[DEBUG] TaskManager收到取消任务请求")
            try:
                # 发送停止信号到officical_process
                try:
                    from wechat.auto_process.officical_process import request_stop
                    request_stop()
                    print("[DEBUG] TaskManager已发送停止信号")
                except ImportError:
                    print("[DEBUG] TaskManager无法导入停止函数")
                    pass

                if hasattr(self.current_task, 'cancel'):
                    self.current_task.cancel()
                    print("[DEBUG] TaskManager已调用任务的cancel方法")

                # 注意：不在这里发送finished信号，让任务自己发送

            except Exception as e:
                print(f"[DEBUG] TaskManager取消任务时出错: {e}")
            finally:
                # 清理任务引用
                self.current_task = None
                print("[DEBUG] TaskManager已清理任务引用")

    def is_task_running(self) -> bool:
        """检查是否有任务正在运行"""
        return self.current_task is not None

    def _on_task_finished(self, success: bool, message: str):
        """任务完成回调，自动清理任务状态"""
        print(f"[DEBUG] TaskManager收到任务完成信号: success={success}, message={message}")
        if self.current_task:
            print(f"[DEBUG] 清理TaskManager中的任务状态")
            self.current_task = None
        else:
            print(f"[DEBUG] TaskManager中没有当前任务，无需清理")
