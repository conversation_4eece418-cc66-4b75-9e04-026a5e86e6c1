import json
import logging
import sys


def send_message(msg_type='log', message='', progress=None):
    """发送消息到主进程"""
    msg = {
        'type': msg_type,
        'message': message
    }
    if progress is not None:
        msg['progress'] = progress

    try:
        # 统一使用UTF-8编码发送消息
        output = json.dumps(msg, ensure_ascii=False, separators=(',', ':')) + '\n'
        sys.stdout.buffer.write(output.encode('utf-8'))
        sys.stdout.flush()
    except (UnicodeEncodeError, TypeError):
        # 处理编码错误，使用ASCII编码作为备用方案
        try:
            print(json.dumps(msg, ensure_ascii=True))
            sys.stdout.flush()
        except Exception:
            # 如果仍然失败，记录错误但不中断程序
            logging.error(f"发送消息失败: {msg_type} - {message}")
