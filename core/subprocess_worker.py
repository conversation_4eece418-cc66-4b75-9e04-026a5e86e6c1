#!/usr/bin/env python3
"""
子进程工作脚本
用于在独立进程中执行关注和采集任务
"""
import logging
import sys
import json
import os
import traceback
from pathlib import Path
from contextlib import contextmanager

from core.common_util import send_message
from wechat.auto_process.officical_process import OfficialProcess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
official_process = OfficialProcess()


@contextmanager
def com_resource_manager():
    """COM资源管理器，统一处理COM初始化和清理"""
    try:
        from core.com_init import initialize_com
        if not initialize_com():
            send_message('log', "COM初始化失败")
            yield False
        else:
            yield True
    finally:
        try:
            from core.com_init import cleanup_com
            cleanup_com()
        except Exception as e:
            send_message('log', f"COM清理异常: {str(e)}")


def flush_outputs():
    """统一的输出刷新函数"""
    try:
        sys.stdout.flush()
        sys.stderr.flush()
    except:
        pass


def handle_task_exception(task_name, exception, context=""):
    """统一的任务异常处理"""
    error_msg = f"{task_name}异常"
    if context:
        error_msg += f" {context}"
    error_msg += f": {str(exception)}"
    send_message('log', error_msg)
    return False

def execute_follow_task(config):
    """执行关注任务"""
    with com_resource_manager() as com_ok:
        if not com_ok:
            return False

        try:
            # 设置微信路径
            official_process.WECHAT_PATH = config['wechat_path']
            accounts = config['accounts']
            total = len(accounts)

            send_message('log', f"开始关注任务，共 {total} 个账号")

            success_count = 0
            follow_results = []

            for i, account in enumerate(accounts):
                send_message('progress', f"正在关注: {account}", int((i / total) * 100))

                try:
                    result = official_process.follow_official(accounts=[account])
                    if result and len(result) > 0:
                        for follow_result in result:
                            if follow_result:
                                success_count += 1
                                follow_results.append(follow_result)
                                official_name = follow_result.get('official_name', account)
                                send_message('log', f"关注成功: {account} -> {official_name}")
                            else:
                                send_message('log', f"关注失败: {account}")
                    else:
                        send_message('log', f"关注失败: {account}")
                except Exception as e:
                    handle_task_exception("关注", e, account)

            # 保存关注结果
            if follow_results:
                try:
                    results_file = os.path.join(os.getcwd(), 'temp_follow_results.json')
                    with open(results_file, 'w', encoding='utf-8') as f:
                        json.dump(follow_results, f, ensure_ascii=False, indent=2)
                    send_message('log', f"关注结果已保存: {len(follow_results)} 个")
                except Exception as e:
                    handle_task_exception("保存关注结果", e)

            send_message('progress', "关注任务完成", 100)
            send_message('log', f"关注任务完成，成功 {success_count}/{total} 个")
            return True

        except Exception as e:
            return handle_task_exception("关注任务", e)
        finally:
            flush_outputs()


def execute_collect_task(config):
    """执行采集任务"""
    with com_resource_manager() as com_ok:
        if not com_ok:
            return False

        try:
            # 设置微信路径
            official_process.WECHAT_PATH = config['wechat_path']
            official_infos = config['official_infos']
            limit_count = config['limit_count']
            stop_exist_count = config['stop_exist_count']
            total = len(official_infos)

            send_message('log', f"开始采集任务，共 {total} 个公众号")
            send_message('log', f"采集参数: limit={limit_count}, stop_exist={stop_exist_count}")

            for i, official in enumerate(official_infos):
                official_name = official['official_name']
                official_account = official['official_account']

                send_message('progress', f"正在采集: {official_name}", int((i / total) * 100))

                try:
                    official_process.collect_official(
                        [official],
                        limit_count=limit_count,
                        stop_exist_count=stop_exist_count
                    )
                    send_message('log', f"采集完成: {official_name}")
                except Exception as e:
                    handle_task_exception("采集", e, f"{official_name} ({official_account})")

            send_message('progress', "采集任务完成", 100)
            send_message('log', f"采集任务完成，共处理 {total} 个公众号")
            return True

        except Exception as e:
            return handle_task_exception("采集任务", e)
        finally:
            flush_outputs()



def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("Usage: subprocess_worker.py <config_file>")
        sys.exit(1)

    config_file = sys.argv[1]
    success = False

    try:
        send_message('log', f"子进程启动: {config_file}")

        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        task_type = config.get('task_type')

        if task_type == 'follow':
            success = execute_follow_task(config)
        elif task_type == 'collect':
            success = execute_collect_task(config)
        else:
            send_message('log', f"未知任务类型: {task_type}")
            success = False

    except Exception as e:
        success = handle_task_exception("子进程执行", e)

    finally:
        # 清理配置文件
        try:
            if os.path.exists(config_file):
                os.remove(config_file)
        except Exception as e:
            send_message('log', f"清理配置文件失败: {str(e)}")

        send_message('log', f"子进程退出: {'成功' if success else '失败'}")
        flush_outputs()

        # 强制退出进程
        os._exit(0 if success else 1)


if __name__ == '__main__':
    main()
