"""
COM组件初始化模块
用于统一管理COM组件的初始化，避免线程模式冲突
"""
import logging
import warnings
import threading

# COM初始化状态
_com_initialized = False
_com_lock = threading.Lock()

def initialize_com():
    """
    初始化COM组件
    确保在整个应用程序中只初始化一次，避免线程模式冲突
    """
    global _com_initialized

    with _com_lock:
        if _com_initialized:
            return True

        # 过滤COM相关警告
        warnings.filterwarnings("ignore", message="Revert to STA COM threading mode", category=UserWarning)
        warnings.filterwarnings("ignore", message=".*COM threading mode.*", category=UserWarning)

        # 优先尝试使用pythoncom（更稳定，兼容性更好）
        try:
            import pythoncom
            try:
                pythoncom.CoInitialize()
                _com_initialized = True
                return True
            except OSError as e:
                if "CoInitialize has already been called" in str(e) or e.winerror == -2147417850:
                    # COM已经初始化，这是正常情况
                    _com_initialized = True
                    return True
                else:
                    raise e
        except (ImportError, OSError):
            pass  # 尝试备用方案

        # 备用方案：尝试使用comtypes
        try:
            import comtypes
            try:
                import comtypes.stream  # 测试子模块是否可用
            except ImportError:
                raise ImportError("comtypes子模块缺失")

            comtypes.CoInitializeEx(comtypes.COINIT_APARTMENTTHREADED)
            _com_initialized = True
            return True

        except (ImportError, OSError):
            pass

        # 如果都失败了，标记为已初始化避免重复尝试
        _com_initialized = True
        return False

def cleanup_com():
    """
    清理COM组件
    """
    global _com_initialized

    with _com_lock:
        if not _com_initialized:
            return

        # 优先尝试pythoncom清理
        try:
            import pythoncom
            try:
                pythoncom.CoUninitialize()
            except OSError:
                pass  # COM已经清理或未初始化，这是正常情况
        except ImportError:
            # 备用方案：尝试comtypes清理
            try:
                import comtypes
                if hasattr(comtypes, 'CoUninitialize'):
                    try:
                        comtypes.CoUninitialize()
                    except OSError:
                        pass
            except (ImportError, AttributeError):
                pass  # 无可用清理方法

        _com_initialized = False

def is_com_initialized():
    """
    检查COM是否已初始化
    """
    return _com_initialized

def safe_initialize_com():
    """
    安全的COM初始化，适用于打包后的环境
    """
    try:
        return initialize_com()
    except Exception:
        # 在打包环境中，即使COM初始化失败也要继续运行
        global _com_initialized
        _com_initialized = True
        return False

def get_com_status():
    """
    获取COM初始化状态信息
    """
    status = {
        'initialized': _com_initialized,
        'pythoncom_available': False,
        'comtypes_available': False,
        'comtypes_stream_available': False
    }

    try:
        import pythoncom
        status['pythoncom_available'] = True
    except ImportError:
        pass

    try:
        import comtypes
        status['comtypes_available'] = True
        try:
            import comtypes.stream
            status['comtypes_stream_available'] = True
        except ImportError:
            pass
    except ImportError:
        pass

    return status

# 自动初始化COM（在模块导入时）
safe_initialize_com()
