# 微信公众号自动化管理工具

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/)
[![PySide6](https://img.shields.io/badge/PySide6-6.9.0-green.svg)](https://pypi.org/project/PySide6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

一个基于 Python 和 PySide6 开发的微信公众号自动化管理工具，支持批量关注公众号、自动采集公众号内容、数据管理和全文下载等功能。

## ✨ 主要功能

### 📱 公众号管理
- **账号管理**: 添加、编辑、删除公众号账号信息
- **批量导入**: 支持从CSV文件批量导入公众号账号
- **数据验证**: 自动验证账号格式和有效性

### 🤖 自动化操作
- **批量关注**: 自动批量关注指定的公众号列表
- **智能采集**: 自动采集公众号文章信息和内容
- **进度监控**: 实时显示任务进度和执行状态
- **错误处理**: 智能错误处理和重试机制

### 📊 数据管理
- **数据查看**: 查看、搜索、筛选采集到的数据
- **数据导出**: 支持导出为CSV、Excel等格式
- **数据去重**: 自动去除重复的文章链接
- **数据备份**: 定期备份数据文件

### 📄 内容下载
- **全文下载**: 下载文章全文为Markdown或HTML格式
- **批量处理**: 支持批量下载多篇文章
- **格式转换**: 自动转换HTML为Markdown格式
- **文件管理**: 按公众号分类存储下载的文件

### ⚙️ 系统功能
- **任务调度**: 支持定时任务和计划任务
- **日志记录**: 详细的操作日志和错误记录
- **配置管理**: 灵活的配置选项和参数设置
- **界面友好**: 现代化的图形用户界面

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.12+
- **微信版本**: 微信PC版 (最新版本)
- **内存**: 建议4GB以上
- **存储**: 建议1GB以上可用空间

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/Wechat-Auto.git
   cd Wechat-Auto
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

### 首次使用

1. **配置微信路径**: 程序会自动检测微信安装路径，如检测失败请手动设置
2. **添加公众号**: 在"账号管理"页面添加要关注或采集的公众号
3. **开始使用**: 选择相应功能开始自动化操作

## 📖 使用指南

### 账号管理

1. **添加单个账号**
   - 点击"添加账号"按钮
   - 填写公众号名称和账号
   - 点击"保存"

2. **批量导入账号**
   - 准备CSV文件，格式：`公众号名称,公众号账号`
   - 点击"导入CSV"选择文件
   - 确认导入

### 批量关注

1. **选择账号**: 在关注管理页面选择要关注的公众号
2. **开始关注**: 点击"开始关注"按钮
3. **监控进度**: 查看实时进度和日志信息
4. **查看结果**: 关注完成后查看成功和失败的统计

### 数据采集

1. **配置参数**:
   - 设置最大采集数量
   - 设置停止条件
   - 选择要采集的公众号

2. **开始采集**: 点击"开始采集"按钮
3. **实时监控**: 查看采集进度和状态
4. **数据查看**: 在数据查看器中查看采集结果

### 数据查看与导出

1. **查看数据**: 在"数据查看"页面浏览采集的数据
2. **搜索筛选**: 使用搜索功能快速找到需要的内容
3. **导出数据**: 选择数据后点击"导出"按钮

## 🏗️ 项目结构

```
Wechat-Auto/
├── main.py                    # 主程序入口
├── requirements.txt           # 依赖包列表
├── build_nuitka.py           # 打包脚本
├── config/                   # 配置模块
│   └── settings.py          # 设置管理
├── core/                     # 核心功能模块
│   ├── account_service.py   # 账号服务
│   ├── official_service.py  # 公众号服务
│   ├── task_manager.py      # 任务管理
│   ├── data_manager.py      # 数据管理
│   ├── official_down.py     # 文章下载
│   ├── wechat_detector.py   # 微信检测
│   ├── scheduler.py         # 任务调度
│   ├── com_init.py          # COM组件初始化
│   ├── common_util.py       # 通用工具
│   └── subprocess_worker.py # 子进程工作器
├── ui/                       # 用户界面模块
│   ├── main_window.py       # 主窗口
│   ├── account_manager.py   # 账号管理界面
│   ├── follow_manager.py    # 关注管理界面
│   ├── collect_manager.py   # 采集管理界面
│   ├── data_viewer.py       # 数据查看界面
│   ├── settings_dialog.py   # 设置对话框
│   ├── message_box.py       # 消息框组件
│   └── components/          # UI组件
├── wechat/                   # 微信自动化模块
│   ├── auto_process/        # 自动化处理
│   └── wechat_ui_api/       # 微信UI API
├── resource/                 # 资源文件
│   ├── config.json          # 配置文件
│   ├── accounts.csv         # 账号数据
│   └── official_infos.json  # 公众号信息
├── data/                     # 数据存储目录
└── downloads/                # 下载文件目录
```

## ⚙️ 技术栈

- **GUI框架**: PySide6 (Qt for Python)
- **自动化**: pywinauto, comtypes
- **数据处理**: pandas, csv
- **网络请求**: requests, httpx
- **文本处理**: BeautifulSoup4, html2text
- **任务调度**: QTimer, threading
- **打包工具**: Nuitka

## 🔧 配置说明

### 主要配置项

```json
{
    "wechat": {
        "wechat_path": "",        // 微信程序路径
        "load_delay": 3,          // 加载延迟(秒)
        "is_maximize": false      // 是否最大化窗口
    },
    "task": {
        "limit_count": 200,       // 最大采集数量
        "stop_exist_count": 30    // 停止存在计数
    },
    "ui": {
        "theme": "light",         // 界面主题
        "auto_size": true         // 自动调整窗口大小
    }
}
```

### 环境变量

- `WECHAT_PATH`: 微信程序路径
- `DEBUG`: 调试模式开关

## 📝 开发指南

### 开发环境搭建

1. **安装开发依赖**
   ```bash
   pip install -r requirements.txt
   pip install pytest black flake8
   ```

2. **代码格式化**
   ```bash
   black .
   flake8 .
   ```

3. **运行测试**
   ```bash
   pytest tests/
   ```

### 打包发布

```bash
python build_nuitka.py
```

## ⚠️ 注意事项

1. **使用限制**
   - 请遵守微信使用条款，避免频繁操作
   - 建议在操作间隔中加入适当延迟
   - 不要同时运行多个实例

2. **安全提醒**
   - 请勿用于商业用途或大规模采集
   - 注意保护个人隐私和数据安全
   - 建议定期备份重要数据

3. **兼容性**
   - 仅支持Windows系统
   - 需要安装微信PC版
   - 可能需要管理员权限

## 🐛 问题反馈

如果您在使用过程中遇到问题，请：

1. 查看日志文件 `wechat_auto.log`
2. 检查配置文件是否正确
3. 确认微信版本兼容性
4. 提交Issue并附上详细信息

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Pull Request和Issue！

## 📞 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-username/Wechat-Auto)
- 问题反馈: [Issues](https://github.com/your-username/Wechat-Auto/issues)

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和平台使用条款。